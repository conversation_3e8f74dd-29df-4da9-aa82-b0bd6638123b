'use client';

import React, { useEffect, useRef, useState, useMemo } from 'react';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkGfm from 'remark-gfm';
import remarkRehype from 'remark-rehype';
import rehypeHighlight from 'rehype-highlight';
import rehypeStringify from 'rehype-stringify';
import rehypeRaw from 'rehype-raw';

// M2 优化模块
import { IncrementalParser, type SegmentChange } from '@/lib/incremental-parser';
import { SemanticScrollManager } from '@/lib/semantic-scroll';
import { AdaptiveDebounceManager, createAdaptiveDebounce } from '@/lib/adaptive-debounce';
import { TOCGenerator, type TOCGenerationResult } from '@/lib/toc-generator';
// 安全导入性能监控
let performanceMonitor: any = null;
try {
  const { performanceMonitor: pm } = require('@/lib/performance-monitor');
  performanceMonitor = pm;
} catch (error) {
  console.warn('Performance monitor not available:', error);
  // 创建简化的性能监控替代
  performanceMonitor = {
    markStart: (name: string) => performance?.mark?.(name + '-start'),
    markEnd: (name: string) => {
      try {
        performance?.mark?.(name + '-end');
        const start = performance?.getEntriesByName?.(name + '-start')?.[0]?.startTime || 0;
        const end = performance?.getEntriesByName?.(name + '-end')?.[0]?.startTime || 0;
        return end - start;
      } catch {
        return 0;
      }
    }
  };
}

// 安全导入安全渲染器
let secureRenderSegment: any = null;
try {
  const { secureRenderSegment: srs } = require('@/lib/secure-renderer');
  secureRenderSegment = srs;
} catch (error) {
  console.warn('Secure renderer not available:', error);
  secureRenderSegment = (html: string) => html; // 回退到直接返回
}

interface ContentSegment {
  id: string;
  content: string;
  renderedHTML: string;
  startOffset: number;
  endOffset: number;
}

interface M2RendererProps {
  content: string;
  onChange?: (content: string) => void;
  onTOCGenerated?: (toc: TOCGenerationResult) => void;
  onScrollPositionChanged?: (position: any) => void;
}

export function NativeDOMRendererM2({ 
  content, 
  onChange, 
  onTOCGenerated, 
  onScrollPositionChanged 
}: M2RendererProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [segments, setSegments] = useState<ContentSegment[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);
  const [m2Status, setM2Status] = useState<string>('初始化中...');

  // M2 核心管理器
  const incrementalParser = useRef<IncrementalParser | null>(null);
  const scrollManager = useRef<SemanticScrollManager | null>(null);
  const debounceManager = useRef<AdaptiveDebounceManager | null>(null);
  const tocGenerator = useRef<TOCGenerator>(new TOCGenerator());

  // Markdown处理器
  const processor = useMemo(
    () =>
      unified()
        .use(remarkParse)
        .use(remarkGfm)
        .use(remarkRehype, { allowDangerousHtml: true })
        .use(rehypeHighlight)
        .use(rehypeRaw)
        .use(rehypeStringify),
    []
  );

  // 初始化管理器
  useEffect(() => {
    if (!containerRef.current || isInitialized) return;

    try {
      console.log('🚀 M2系统初始化开始...');

      // 初始化增量解析器
      incrementalParser.current = new IncrementalParser(content);
      console.log('✅ 增量解析器初始化完成');

      // 初始化语义滚动管理器
      scrollManager.current = new SemanticScrollManager(containerRef.current);
      console.log('✅ 语义滚动管理器初始化完成');

      // 初始化自适应防抖管理器
      debounceManager.current = new AdaptiveDebounceManager({
        minDelay: 60,
        maxDelay: 400,
        baseDelay: 120
      });
      console.log('✅ 自适应防抖管理器初始化完成');

      setIsInitialized(true);
      setM2Status('✅ M2系统初始化完成');

      console.log('🎉 M2优化系统全部初始化完成！');

    } catch (error: any) {
      console.error('M2初始化失败:', error);
      setM2Status('❌ M2初始化失败: ' + (error?.message || 'Unknown error'));
    }

    return () => {
      scrollManager.current?.destroy();
    };
  }, [content, isInitialized]);

  // 自适应防抖的内容处理
  const debouncedProcessContent = useMemo(() => {
    if (!debounceManager.current) return null;

    return createAdaptiveDebounce(async (newContent: string) => {
      if (!incrementalParser.current || !containerRef.current) return;

      const startTime = performance?.now?.() || 0;
      performanceMonitor?.markStart?.('m2-parse');

      try {
        console.log('🔄 M2处理开始...');

        // 1. 生成TOC
        const tocResult = tocGenerator.current.generateTOC(newContent);
        onTOCGenerated?.(tocResult);
        console.log('📚 TOC生成完成:', tocResult.toc.length, '个标题');

        // 2. 增量解析（简化版本 - 完全重新解析）
        const parseResult = incrementalParser.current.forceReparse(newContent);
        console.log('🔧 增量解析完成:', parseResult.changes.length, '个变化');
        
        // 3. 处理段落变化
        const newSegments = await Promise.all(
          parseResult.changes
            .filter(change => change.type === 'add' || change.type === 'update')
            .map(async (change) => {
              const renderedHTML = await renderSegmentContent(change.content);
              return {
                id: change.segmentId,
                content: change.content,
                renderedHTML,
                startOffset: change.startOffset,
                endOffset: change.endOffset
              };
            })
        );

        // 4. 更新DOM
        performanceMonitor?.markStart?.('m2-dom-update');
        await updateDOMWithM2Changes(parseResult.changes, newSegments);
        const domUpdateTime = performanceMonitor?.markEnd?.('m2-dom-update') || 0;
        console.log('🎨 DOM更新完成:', domUpdateTime.toFixed(1) + 'ms');

        // 5. 恢复滚动位置
        if (scrollManager.current) {
          setTimeout(() => {
            scrollManager.current?.restorePosition();
          }, 50);
        }

        setSegments(newSegments);
        console.log('✅ M2处理完成');

      } catch (error) {
        console.error('M2处理失败:', error);
        // 回退到简单渲染
        await fallbackRender(newContent);
      } finally {
        const totalTime = performanceMonitor?.markEnd?.('m2-parse') || 
                         ((performance?.now?.() || 0) - startTime);
        
        // 记录性能用于自适应防抖
        if (debounceManager.current) {
          debounceManager.current.calculateDebounceDelay(newContent, totalTime);
        }
        
        console.log('⏱️ M2总耗时:', totalTime.toFixed(1) + 'ms');
      }
    }, debounceManager.current);
  }, [debounceManager.current, onTOCGenerated]);

  // 内容变化处理
  useEffect(() => {
    if (!debouncedProcessContent || !content) return;

    // 捕获当前滚动位置
    if (scrollManager.current) {
      scrollManager.current.captureCurrentPosition();
    }

    debouncedProcessContent(content);
  }, [content, debouncedProcessContent]);

  // 渲染单个段落
  const renderSegmentContent = async (segmentContent: string): Promise<string> => {
    try {
      const result = await processor.process(segmentContent);
      return secureRenderSegment ? secureRenderSegment(String(result)) : String(result);
    } catch (error) {
      console.error('段落渲染失败:', error);
      return `<p>渲染错误: ${segmentContent.substring(0, 100)}...</p>`;
    }
  };

  // M2增强的DOM更新
  const updateDOMWithM2Changes = async (
    changes: SegmentChange[], 
    newSegments: ContentSegment[]
  ): Promise<void> => {
    if (!containerRef.current) return;

    const container = containerRef.current;
    const segmentMap = new Map(newSegments.map(seg => [seg.id, seg]));

    // 如果是完全重建，直接替换所有内容
    container.innerHTML = '';
    const fragment = document.createDocumentFragment();

    newSegments.forEach(segment => {
      const wrapper = document.createElement('div');
      wrapper.setAttribute('data-segment-id', segment.id);
      wrapper.setAttribute('data-start-offset', segment.startOffset.toString());
      wrapper.setAttribute('data-end-offset', segment.endOffset.toString());
      wrapper.innerHTML = segment.renderedHTML;
      fragment.appendChild(wrapper);
    });

    container.appendChild(fragment);

    // 更新语义锚点
    if (scrollManager.current) {
      scrollManager.current.scanForAnchors();
    }
  };

  // 回退渲染方案
  const fallbackRender = async (content: string): Promise<void> => {
    if (!containerRef.current) return;

    try {
      console.log('🔄 使用回退渲染...');
      const result = await processor.process(content);
      const secureHTML = secureRenderSegment ? secureRenderSegment(String(result)) : String(result);
      
      const wrapper = document.createElement('div');
      wrapper.setAttribute('data-segment-id', 'fallback');
      wrapper.innerHTML = secureHTML;
      
      containerRef.current.innerHTML = '';
      containerRef.current.appendChild(wrapper);
    } catch (error) {
      console.error('回退渲染失败:', error);
      if (containerRef.current) {
        containerRef.current.innerHTML = '<p>渲染失败</p>';
      }
    }
  };

  // 初始渲染
  useEffect(() => {
    if (!isInitialized || !content) return;

    // 首次渲染
    debouncedProcessContent?.(content);
  }, [isInitialized, content, debouncedProcessContent]);

  // 调试控制台命令 - 简化版本
  useEffect(() => {
    if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
      (window as any).m2Debug = {
        getIncrementalParser: () => incrementalParser.current,
        getScrollManager: () => scrollManager.current,
        getDebounceManager: () => debounceManager.current,
        getTOCGenerator: () => tocGenerator.current,
        getSegments: () => segments,
        getStatus: () => m2Status,
        debugAll: () => {
          console.log('=== M2调试信息 ===');
          console.log('状态:', m2Status);
          console.log('段落数:', segments.length);
          incrementalParser.current?.debug();
          scrollManager.current?.debug();
          debounceManager.current?.debug();
          tocGenerator.current.debug();
        }
      };

      console.log('🔧 M2调试接口已注册，使用 m2Debug.debugAll() 查看状态');
    }
  }, [segments, m2Status]);

  return (
    <div className="relative">
      {/* M2状态指示器 */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-20 right-4 bg-blue-900 text-white px-2 py-1 rounded text-xs z-50">
          {m2Status}
        </div>
      )}
      
      <div 
        ref={containerRef}
        className="prose prose-sm sm:prose-base lg:prose-lg xl:prose-xl 2xl:prose-2xl mx-auto focus:outline-none"
        style={{
          maxWidth: 'none',
          lineHeight: '1.7',
          fontSize: '14px'
        }}
      />
    </div>
  );
}

// 默认导出保持兼容性
export default NativeDOMRendererM2; 