'use client';

import { useState, useEffect } from 'react';
import { performanceMonitor, type PerformanceMetrics } from '@/lib/performance-monitor';

interface PerformanceDisplayProps {
  enabled?: boolean;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
}

export function PerformanceDisplay({ 
  enabled = true, 
  position = 'top-right' 
}: PerformanceDisplayProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [isVisible, setIsVisible] = useState(enabled);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!isVisible) return;

    const interval = setInterval(() => {
      const currentMetrics = performanceMonitor.getMetrics();
      setMetrics(currentMetrics);
    }, 1000);

    return () => clearInterval(interval);
  }, [isVisible]);

  // 键盘快捷键切换显示
  useEffect(() => {
    const handleKeydown = (e: KeyboardEvent) => {
      // Ctrl/Cmd + Shift + P 切换性能面板
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'P') {
        e.preventDefault();
        setIsVisible(prev => !prev);
        console.log('性能面板状态:', !isVisible ? '显示' : '隐藏');
      }
    };

    window.addEventListener('keydown', handleKeydown);
    return () => window.removeEventListener('keydown', handleKeydown);
  }, [isVisible]);

  if (!isVisible || !metrics) return null;

  const getPositionStyles = () => {
    const baseStyles = {
      position: 'fixed' as const,
      background: 'rgba(0, 0, 0, 0.9)',
      color: 'white',
      padding: '12px',
      borderRadius: '8px',
      fontSize: '11px',
      fontFamily: 'monospace',
      zIndex: 10000,
      minWidth: '160px',
      backdropFilter: 'blur(4px)',
      border: '1px solid rgba(255, 255, 255, 0.1)'
    };

    switch (position) {
      case 'top-left':
        return { ...baseStyles, top: 10, left: 10 };
      case 'top-right':
        return { ...baseStyles, top: 10, right: 10 };
      case 'bottom-left':
        return { ...baseStyles, bottom: 10, left: 10 };
      case 'bottom-right':
        return { ...baseStyles, bottom: 10, right: 10 };
      default:
        return { ...baseStyles, top: 10, right: 10 };
    }
  };

  const getStatusColor = (value: number, threshold: number, reverse = false) => {
    const isGood = reverse ? value < threshold : value >= threshold;
    return isGood ? '#10b981' : '#ef4444';
  };

  return (
    <div style={getPositionStyles()}>
      <div style={{ 
        fontSize: '12px', 
        fontWeight: 'bold', 
        marginBottom: '8px',
        borderBottom: '1px solid rgba(255,255,255,0.2)',
        paddingBottom: '4px'
      }}>
        📊 M1性能监控
      </div>
      
      <div style={{ lineHeight: '1.4' }}>
        <div style={{ color: getStatusColor(metrics.fps, 50) }}>
          FPS: {metrics.fps.toFixed(1)} 
          <span style={{ color: '#888', fontSize: '10px' }}>
            {metrics.fps >= 55 ? ' 优秀' : metrics.fps >= 50 ? ' 良好' : ' 需优化'}
          </span>
        </div>
        
        <div style={{ color: getStatusColor(metrics.inputLatency.p95, 100, true) }}>
          P95延迟: {metrics.inputLatency.p95.toFixed(1)}ms
          <span style={{ color: '#888', fontSize: '10px' }}>
            {metrics.inputLatency.p95 < 80 ? ' 优秀' : metrics.inputLatency.p95 < 150 ? ' 良好' : ' 需优化'}
          </span>
        </div>
        
        <div style={{ color: getStatusColor(metrics.longTasks.maxDuration, 50, true) }}>
          长任务: {metrics.longTasks.count}次
          <span style={{ color: '#888', fontSize: '10px' }}>
            {metrics.longTasks.maxDuration < 50 ? ' 优秀' : metrics.longTasks.maxDuration < 80 ? ' 良好' : ' 需优化'}
          </span>
        </div>
        
        <div style={{ color: getStatusColor(metrics.domNodeCount, 2500, true) }}>
          DOM节点: {metrics.domNodeCount}
          <span style={{ color: '#888', fontSize: '10px' }}>
            {metrics.domNodeCount <= 2500 ? ' 合理' : ' 过多'}
          </span>
        </div>
        
        {metrics.memoryUsage && (
          <div style={{ color: '#60a5fa' }}>
            内存: {metrics.memoryUsage}MB
          </div>
        )}
      </div>

      {mounted && (
        <div style={{ 
          marginTop: '8px', 
          paddingTop: '4px',
          borderTop: '1px solid rgba(255,255,255,0.2)',
          fontSize: '9px',
          color: '#888'
        }}>
          快捷键: {navigator.platform.includes('Mac') ? 'Cmd' : 'Ctrl'}+Shift+P 切换显示
        </div>
      )}
    </div>
  );
} 