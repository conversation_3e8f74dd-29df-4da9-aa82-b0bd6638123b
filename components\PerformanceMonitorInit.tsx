'use client';

import { useEffect } from 'react';
import { performanceMonitor } from '@/lib/performance-monitor';

export function PerformanceMonitorInit() {
  useEffect(() => {
    // 初始化性能监控
    console.log('🚀 初始化M1性能监控系统...');
    performanceMonitor.setEnabled(true);
    
    // 在开发环境每30秒输出性能报告
    if (process.env.NODE_ENV === 'development') {
      console.log('📊 开发环境：启用性能报告（每30秒输出一次）');
      
      const interval = setInterval(() => {
        const report = performanceMonitor.generateReport();
        console.log(report);
      }, 30000);
      
      // 5秒后输出第一次报告
      setTimeout(() => {
        console.log('📊 M1性能监控首次报告：');
        console.log(performanceMonitor.generateReport());
      }, 5000);
      
      return () => clearInterval(interval);
    }
  }, []);

  // 开发环境显示性能监控状态
  if (process.env.NODE_ENV === 'development') {
    return (
      <div
        style={{
          position: 'fixed',
          bottom: 10,
          right: 10,
          backgroundColor: '#10b981',
          color: 'white',
          padding: '4px 8px',
          borderRadius: '4px',
          fontSize: '10px',
          zIndex: 9999,
          fontFamily: 'monospace'
        }}
        title="M1性能监控已启用 - 查看控制台获取详细报告"
      >
        M1监控✓
      </div>
    );
  }

  return null;
} 