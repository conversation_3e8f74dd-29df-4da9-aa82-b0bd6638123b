'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { usePerformanceTest } from '@/hooks/use-performance-test';

export function M1PerformanceTestPanel() {
  const [isVisible, setIsVisible] = useState(false);
  const [mounted, setMounted] = useState(false);
  const { isRunning, lastResult, runTest, generateTestReport, resetMetrics } = usePerformanceTest();

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleRunTest = async () => {
    const result = await runTest();
    const report = generateTestReport();
    
    if (report) {
      console.log(report);
    }
    
    // 显示浮动通知
    if (result.overall === 'pass') {
      console.log('🎉 M1性能验收通过！');
    } else {
      console.log('⚠️ M1性能验收未通过，请查看详细报告。');
    }
  };

  const handleDownloadReport = () => {
    const report = generateTestReport();
    if (!report) return;

    const blob = new Blob([report], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `M1性能测试报告_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 快捷键控制
  useEffect(() => {
    const handleKeydown = (e: KeyboardEvent) => {
      // Ctrl/Cmd + Shift + T 打开测试面板
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'T') {
        e.preventDefault();
        setIsVisible(prev => !prev);
        console.log('测试面板状态:', !isVisible ? '显示' : '隐藏');
      }
    };

    window.addEventListener('keydown', handleKeydown);
    return () => window.removeEventListener('keydown', handleKeydown);
  }, [isVisible]);

  if (!isVisible) {
    return (
      <div
        style={{
          position: 'fixed',
          bottom: 10,
          left: 10,
          zIndex: 9999
        }}
      >
        <Button
          onClick={() => setIsVisible(true)}
          size="sm"
          variant="outline"
          className="text-xs"
        >
          M1测试
        </Button>
      </div>
    );
  }

  const getStatusBadge = (status: 'pass' | 'fail') => (
    <Badge variant={status === 'pass' ? 'default' : 'destructive'}>
      {status === 'pass' ? '✅ 通过' : '❌ 未通过'}
    </Badge>
  );

  return (
    <div
      style={{
        position: 'fixed',
        bottom: 20,
        left: 20,
        right: 20,
        maxWidth: '480px',
        zIndex: 9999
      }}
    >
      <Card className="shadow-lg">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">M1性能验收测试</CardTitle>
            <Button
              onClick={() => setIsVisible(false)}
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
            >
              ×
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* 测试控制 */}
          <div className="flex gap-2">
            <Button
              onClick={handleRunTest}
              disabled={isRunning}
              size="sm"
            >
              {isRunning ? '测试中...' : '开始测试'}
            </Button>
            
            <Button
              onClick={resetMetrics}
              variant="outline"
              size="sm"
            >
              重置指标
            </Button>
            
            {lastResult && (
              <Button
                onClick={handleDownloadReport}
                variant="outline"
                size="sm"
              >
                下载报告
              </Button>
            )}
          </div>

          {/* 测试结果 */}
          {lastResult && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="font-medium">总体结果:</span>
                {getStatusBadge(lastResult.overall)}
              </div>
              
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="flex justify-between">
                  <span>帧率 (≥50):</span>
                  <span className={lastResult.fps.status === 'pass' ? 'text-green-600' : 'text-red-600'}>
                    {lastResult.fps.value.toFixed(1)}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span>延迟 (&lt;100ms):</span>
                  <span className={lastResult.inputLatency.status === 'pass' ? 'text-green-600' : 'text-red-600'}>
                    {lastResult.inputLatency.value.toFixed(1)}ms
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span>长任务 (&lt;80ms):</span>
                  <span className={lastResult.longTasks.status === 'pass' ? 'text-green-600' : 'text-red-600'}>
                    {lastResult.longTasks.value.toFixed(1)}ms
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span>DOM节点 (≤2500):</span>
                  <span className={lastResult.domNodes.status === 'pass' ? 'text-green-600' : 'text-red-600'}>
                    {lastResult.domNodes.value}
                  </span>
                </div>
              </div>
              
              <div className="text-xs text-gray-500">
                测试时间: {lastResult.timestamp.toLocaleString()}
              </div>
            </div>
          )}

          {/* 快捷键提示 */}
          {mounted && (
            <div className="text-xs text-gray-500 border-t pt-2">
              快捷键: {navigator.platform.includes('Mac') ? 'Cmd' : 'Ctrl'}+Shift+T 打开/关闭测试面板
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 