'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { usePerformanceTest } from '@/hooks/use-performance-test';

export function QuickTestButton() {
  const { isRunning, runTest, generateTestReport } = usePerformanceTest();
  const [showResult, setShowResult] = useState(false);
  const [testResult, setTestResult] = useState<any>(null);
  const [isMac, setIsMac] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setIsMac(navigator.platform.includes('Mac'));
    setMounted(true);
  }, []);

  const handleQuickTest = async () => {
    const result = await runTest();
    setTestResult(result);
    setShowResult(true);
    
    const report = generateTestReport();
    if (report) {
      console.log(report);
    }
    
    // 3秒后自动隐藏结果
    setTimeout(() => setShowResult(false), 3000);
  };

  // 防止hydration错误，服务器端不渲染快捷键提示
  if (!mounted) {
    return (
      <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border p-3">
          <div className="flex items-center gap-3">
            <Button
              onClick={handleQuickTest}
              disabled={isRunning}
              size="sm"
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isRunning ? '测试中...' : '🧪 M1性能测试'}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50">
      {showResult && testResult ? (
        <div className={`
          px-4 py-2 rounded-lg shadow-lg border-2 text-sm font-medium
          ${testResult.overall === 'pass' 
            ? 'bg-green-50 border-green-200 text-green-800' 
            : 'bg-red-50 border-red-200 text-red-800'
          }
        `}>
          <div className="flex items-center gap-2">
            <span>{testResult.overall === 'pass' ? '🎉' : '⚠️'}</span>
            <span>
              M1验收 {testResult.overall === 'pass' ? '通过' : '未通过'}
            </span>
            <span className="text-xs opacity-70">
              ({testResult.fps.value.toFixed(1)}fps, {testResult.inputLatency.value.toFixed(1)}ms)
            </span>
          </div>
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border p-3">
          <div className="flex items-center gap-3">
            <Button
              onClick={handleQuickTest}
              disabled={isRunning}
              size="sm"
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isRunning ? '测试中...' : '🧪 M1性能测试'}
            </Button>
            <div className="text-xs text-gray-500">
              或按 <kbd className="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-xs">
                {isMac ? 'Cmd' : 'Ctrl'}</kbd>+<kbd className="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-xs">Shift</kbd>+<kbd className="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-xs">T</kbd>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 