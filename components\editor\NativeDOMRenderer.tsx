'use client';

import React, { useRef, useEffect, useCallback } from 'react';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkGfm from 'remark-gfm';
import remarkRehype from 'remark-rehype';
import rehypeHighlight from 'rehype-highlight';
import rehypeStringify from 'rehype-stringify';
import rehypeRaw from 'rehype-raw';
import { BoundaryIndex } from '@/lib/boundary-index';
import { generateStableSegmentId, fastHash32 } from '@/lib/hash-utils';
import { secureRenderSegment } from '@/lib/secure-renderer';
import { performanceMonitor } from '@/lib/performance-monitor';

interface NativeDOMRendererProps {
  content: string;
  className?: string;
  initialScrollPosition?: number;
  onScrollPositionChange?: (position: number) => void;
}

interface ContentSegment {
  id: string;
  content: string;
  hash: string; // 内容哈希，用于检测变化
  startOffset: number; // 段落在文档中的起始字符偏移量
  endOffset: number; // 段落在文档中的结束字符偏移量
  element?: HTMLElement;
}

// 使用新的快速哈希函数（已移至hash-utils.ts）
// const hashString = fastHash32; // 兼容性别名

// M1: 使用安全的段落渲染器
const renderSegment = secureRenderSegment;

// 分割内容为段落 - 使用稳定的段落ID
const splitContent = (content: string): ContentSegment[] => {
  if (!content.trim()) return [];

  const lines = content.split('\n');
  const segments: ContentSegment[] = [];
  const boundaryIndex = new BoundaryIndex(content);
  
  let currentSegment = '';
  let segmentStartLine = 0;
  let inCodeBlock = false;

  const createSegment = (segmentContent: string, startLine: number, endLine: number): ContentSegment => {
    const trimmedContent = segmentContent.trim();
    const startOffset = boundaryIndex.getLineStart(startLine);
    const endOffset = boundaryIndex.getLineStart(endLine + 1) - 1; // 减1避免包含下一行的开始
    
    return {
      id: generateStableSegmentId(trimmedContent, startOffset),
      content: trimmedContent,
      hash: fastHash32(trimmedContent),
      startOffset,
      endOffset: Math.min(endOffset, content.length - 1)
    };
  };

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // 检测代码块边界
    if (line.trim().startsWith('```')) {
      inCodeBlock = !inCodeBlock;
      currentSegment += (currentSegment ? '\n' : '') + line;
      
      // 代码块结束时创建段落
      if (!inCodeBlock && currentSegment.trim()) {
        segments.push(createSegment(currentSegment, segmentStartLine, i));
        currentSegment = '';
        segmentStartLine = i + 1;
      }
      continue;
    }

    if (inCodeBlock) {
      currentSegment += (currentSegment ? '\n' : '') + line;
      continue;
    }

    // 空行分割段落（非代码块内）
    if (line.trim() === '') {
      if (currentSegment.trim() !== '') {
        segments.push(createSegment(currentSegment, segmentStartLine, i - 1));
        currentSegment = '';
      }
      segmentStartLine = i + 1;
    } else {
      currentSegment += (currentSegment ? '\n' : '') + line;
    }
  }

  // 处理最后一个段落
  if (currentSegment.trim() !== '') {
    segments.push(createSegment(currentSegment, segmentStartLine, lines.length - 1));
  }

  return segments;
};

export default function NativeDOMRenderer({
  content,
  className = '',
  initialScrollPosition,
  onScrollPositionChange
}: NativeDOMRendererProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const segmentsRef = useRef<ContentSegment[]>([]);
  const scrollRestoredRef = useRef(false);

  // M1: 实现keyed-diff算法 - 精确增量DOM更新
  const updateDOMKeyed = useCallback((newSegments: ContentSegment[]) => {
    if (!containerRef.current) return;

    const container = containerRef.current;
    const oldSegments = segmentsRef.current;
    
    // 构建映射
    const oldMap = new Map(oldSegments.map((seg) => [seg.id, seg]));
    const newMap = new Map(newSegments.map((seg) => [seg.id, seg]));
    
    // 获取现有DOM元素映射
    const existingElements = Array.from(container.children) as HTMLElement[];
    const elementMap = new Map<string, HTMLElement>();
    
    existingElements.forEach(el => {
      const segmentId = el.getAttribute('data-segment-id');
      if (segmentId) {
        elementMap.set(segmentId, el);
      }
    });

    // 计算需要的操作：add/remove/update/move
    const operations: Array<{
      type: 'add' | 'remove' | 'update' | 'move';
      segmentId: string;
      element?: HTMLElement;
      newIndex?: number;
      content?: string;
    }> = [];

    // 1. 标记需要删除的元素
    oldSegments.forEach(oldSeg => {
      if (!newMap.has(oldSeg.id)) {
        operations.push({
          type: 'remove',
          segmentId: oldSeg.id,
          element: elementMap.get(oldSeg.id)
        });
      }
    });

    // 2. 处理新增、更新和移动
    newSegments.forEach((newSeg, index) => {
      const oldSeg = oldMap.get(newSeg.id);
      const existingElement = elementMap.get(newSeg.id);

      if (!oldSeg) {
        // 新增段落
        operations.push({
          type: 'add',
          segmentId: newSeg.id,
          newIndex: index,
          content: newSeg.content
        });
      } else if (oldSeg.hash !== newSeg.hash) {
        // 内容变化，需要更新
        operations.push({
          type: 'update',
          segmentId: newSeg.id,
          element: existingElement,
          content: newSeg.content
        });
      } else if (existingElement) {
        // 内容未变化，检查位置是否需要调整
        const currentIndex = Array.from(container.children).indexOf(existingElement);
        if (currentIndex !== index) {
          operations.push({
            type: 'move',
            segmentId: newSeg.id,
            element: existingElement,
            newIndex: index
          });
        }
      }
    });

    // 3. 执行DOM操作 - 避免innerHTML清空
    // 先删除不需要的元素
    operations.filter(op => op.type === 'remove').forEach(op => {
      if (op.element && op.element.parentNode) {
        op.element.parentNode.removeChild(op.element);
      }
    });

    // 构建新的有序子节点列表
    const newChildren: HTMLElement[] = [];
    newSegments.forEach((segment) => {
      const existingElement = elementMap.get(segment.id);
      const op = operations.find(o => o.segmentId === segment.id);

      if (op?.type === 'add') {
        // 创建新元素
        const div = document.createElement('div');
        div.className = 'markdown-segment';
        div.setAttribute('data-segment-id', segment.id);
        div.innerHTML = renderSegment(segment.content);
        newChildren.push(div);
        segment.element = div;
      } else if (op?.type === 'update' && existingElement) {
        // 更新内容
        existingElement.innerHTML = renderSegment(segment.content);
        newChildren.push(existingElement);
        segment.element = existingElement;
      } else if (existingElement) {
        // 复用元素
        newChildren.push(existingElement);
        segment.element = existingElement;
      }
    });

    // ✅ 关键改进：不再使用innerHTML = ''
    // 使用DocumentFragment一次性重新排列所有子节点
    const fragment = document.createDocumentFragment();
    newChildren.forEach(child => fragment.appendChild(child));
    
    // 清空容器并重新组装（避免布局抖动）
    while (container.firstChild) {
      container.removeChild(container.firstChild);
    }
    container.appendChild(fragment);
    
    // 更新引用
    segmentsRef.current = newSegments;
  }, []);

  // 监听内容变化 - 使用新的keyed-diff算法 + 性能监控
  useEffect(() => {
    performanceMonitor.markStart('content-parsing');
    const newSegments = splitContent(content);
    const parseTime = performanceMonitor.markEnd('content-parsing');
    
    performanceMonitor.markStart('dom-update');
    updateDOMKeyed(newSegments);
    const updateTime = performanceMonitor.markEnd('dom-update');
    
    // 记录输入延迟（解析+渲染总时间）
    performanceMonitor.recordInputLatency(parseTime + updateTime);
    
    // 在开发环境输出性能信息
    if (process.env.NODE_ENV === 'development' && (parseTime + updateTime) > 50) {
      console.log(`📊 渲染性能: 解析${parseTime.toFixed(2)}ms + 更新${updateTime.toFixed(2)}ms = ${(parseTime + updateTime).toFixed(2)}ms`);
    }
  }, [content, updateDOMKeyed]);

  // M1: 改进的滚动位置恢复 - 避免强制设置
  useEffect(() => {
    if (containerRef.current && 
        initialScrollPosition !== undefined && 
        !scrollRestoredRef.current) {
      
      requestAnimationFrame(() => {
        if (containerRef.current) {
          // 使用scrollTo替代直接设置scrollTop，提供更平滑的体验
          containerRef.current.scrollTo({
            top: initialScrollPosition,
            behavior: 'auto' // M2阶段将改为语义锚点定位
          });
          scrollRestoredRef.current = true;
        }
      });
    }
  }, [initialScrollPosition]);

  // 滚动事件监听
  useEffect(() => {
    const container = containerRef.current;
    if (!container || !onScrollPositionChange) return;

    const handleScroll = () => {
      onScrollPositionChange(container.scrollTop);
    };

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, [onScrollPositionChange]);

  // 监听跳转事件
  useEffect(() => {
    const handleScrollToPreviewHeading = (event: CustomEvent<{ headingId: string }>) => {
      if (containerRef.current) {
        const headingElement = containerRef.current.querySelector(`#${event.detail.headingId}`);
        if (headingElement) {
          headingElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }
    };

    document.addEventListener('scrollToPreviewHeading', handleScrollToPreviewHeading as EventListener);
    
    return () => {
      document.removeEventListener('scrollToPreviewHeading', handleScrollToPreviewHeading as EventListener);
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className={`prose prose-slate dark:prose-invert max-w-none p-4 overflow-auto h-full ${className}`}
      style={{
        lineHeight: '1.7',
        fontSize: '16px'
      }}
    />
  );
} 