import { useState, useCallback } from 'react';
import { performanceMonitor, type PerformanceMetrics } from '@/lib/performance-monitor';

export interface PerformanceTestResult {
  fps: { value: number; status: 'pass' | 'fail'; threshold: number };
  inputLatency: { value: number; status: 'pass' | 'fail'; threshold: number };
  longTasks: { value: number; status: 'pass' | 'fail'; threshold: number };
  domNodes: { value: number; status: 'pass' | 'fail'; threshold: number };
  overall: 'pass' | 'fail';
  timestamp: Date;
}

export function usePerformanceTest() {
  const [isRunning, setIsRunning] = useState(false);
  const [lastResult, setLastResult] = useState<PerformanceTestResult | null>(null);

  const runTest = useCallback(async (): Promise<PerformanceTestResult> => {
    setIsRunning(true);
    
    try {
      // 等待一小段时间确保数据稳定
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const metrics = performanceMonitor.getMetrics();
      
      const result: PerformanceTestResult = {
        fps: {
          value: metrics.fps,
          status: metrics.fps >= 50 ? 'pass' : 'fail',
          threshold: 50
        },
        inputLatency: {
          value: metrics.inputLatency.p95,
          status: metrics.inputLatency.p95 < 100 ? 'pass' : 'fail',
          threshold: 100
        },
        longTasks: {
          value: metrics.longTasks.maxDuration,
          status: metrics.longTasks.maxDuration < 80 ? 'pass' : 'fail',
          threshold: 80
        },
        domNodes: {
          value: metrics.domNodeCount,
          status: metrics.domNodeCount <= 2500 ? 'pass' : 'fail',
          threshold: 2500
        },
        overall: 'pass', // 会在下面更新
        timestamp: new Date()
      };

      // 计算总体结果
      const allTests = [result.fps, result.inputLatency, result.longTasks, result.domNodes];
      result.overall = allTests.every(test => test.status === 'pass') ? 'pass' : 'fail';

      setLastResult(result);
      return result;
    } finally {
      setIsRunning(false);
    }
  }, []);

  const generateTestReport = useCallback(() => {
    if (!lastResult) return null;

    const statusEmoji = (status: 'pass' | 'fail') => status === 'pass' ? '✅' : '❌';
    
    return `
M1性能验收测试报告
═══════════════════════════════════
测试时间: ${lastResult.timestamp.toLocaleString()}
总体结果: ${statusEmoji(lastResult.overall)} ${lastResult.overall.toUpperCase()}

详细指标:
───────────────────────────────────
🎮 帧率 (FPS): ${statusEmoji(lastResult.fps.status)} ${lastResult.fps.value.toFixed(1)} (目标: ≥${lastResult.fps.threshold})
⌨️ 输入延迟: ${statusEmoji(lastResult.inputLatency.status)} ${lastResult.inputLatency.value.toFixed(1)}ms (目标: <${lastResult.inputLatency.threshold}ms)
⏱️ 长任务: ${statusEmoji(lastResult.longTasks.status)} ${lastResult.longTasks.value.toFixed(1)}ms (目标: <${lastResult.longTasks.threshold}ms)
🌐 DOM节点: ${statusEmoji(lastResult.domNodes.status)} ${lastResult.domNodes.value} (目标: ≤${lastResult.domNodes.threshold})

${lastResult.overall === 'pass' 
  ? '🎉 恭喜！M1阶段性能优化验收通过！' 
  : '⚠️ 需要继续优化，部分指标未达标准。'
}
═══════════════════════════════════`;
  }, [lastResult]);

  const resetMetrics = useCallback(() => {
    performanceMonitor.reset();
    console.log('📊 性能指标已重置');
  }, []);

  return {
    isRunning,
    lastResult,
    runTest,
    generateTestReport,
    resetMetrics
  };
} 