/**
 * 性能监控系统 - M1阶段实现
 * 监控关键性能指标：长任务、FPS、输入延迟、DOM节点数等
 */

export interface PerformanceMetrics {
  longTasks: {
    count: number;
    maxDuration: number;
    avgDuration: number;
    recentTasks: number[]; // 最近10个长任务的持续时间
  };
  fps: number;
  inputLatency: {
    p95: number;
    recent: number[]; // 最近20次输入延迟
  };
  domNodeCount: number;
  memoryUsage?: number; // MB
}

export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private longTasks: number[] = [];
  private inputLatencies: number[] = [];
  private observer?: PerformanceObserver;
  private fpsCounter = new FPSCounter();
  private isEnabled = true;

  private constructor() {
    this.init();
  }

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  private init() {
    this.initLongTaskObserver();
    this.fpsCounter.start();
  }

  private initLongTaskObserver() {
    // 检查是否在浏览器环境中
    if (typeof window === 'undefined') return;
    
    if ('PerformanceObserver' in window) {
      try {
        this.observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'longtask') {
              this.recordLongTask(entry.duration);
            }
          }
        });
        this.observer.observe({ entryTypes: ['longtask'] });
      } catch (error) {
        console.warn('Long task observer not supported:', error);
      }
    }
  }

  private recordLongTask(duration: number) {
    this.longTasks.push(duration);
    // 只保留最近100个长任务
    if (this.longTasks.length > 100) {
      this.longTasks = this.longTasks.slice(-100);
    }
    
    if (duration > 50) {
      console.warn(`⚠️ Long task detected: ${duration.toFixed(2)}ms`);
    }
  }

  /**
   * 标记性能测量开始点
   */
  markStart(name: string): void {
    if (!this.isEnabled || typeof performance === 'undefined') return;
    performance.mark(`${name}-start`);
  }

  /**
   * 标记性能测量结束点并计算耗时
   */
  markEnd(name: string): number {
    if (!this.isEnabled || typeof performance === 'undefined') return 0;
    
    const endMark = `${name}-end`;
    performance.mark(endMark);
    
    try {
      const measureName = `measure-${name}`;
      performance.measure(measureName, `${name}-start`, endMark);
      
      const entries = performance.getEntriesByName(measureName, 'measure');
      const duration = entries[entries.length - 1]?.duration || 0;
      
      // 清理标记
      performance.clearMarks(`${name}-start`);
      performance.clearMarks(endMark);
      performance.clearMeasures(measureName);
      
      return duration;
    } catch (error) {
      console.warn('Performance measurement failed:', error);
      return 0;
    }
  }

  /**
   * 记录输入延迟
   */
  recordInputLatency(latency: number): void {
    this.inputLatencies.push(latency);
    // 只保留最近50次
    if (this.inputLatencies.length > 50) {
      this.inputLatencies = this.inputLatencies.slice(-50);
    }
  }

  /**
   * 获取当前DOM节点数量
   */
  getDOMNodeCount(): number {
    if (typeof document === 'undefined') return 0;
    return document.querySelectorAll('*').length;
  }

  /**
   * 获取内存使用情况（如果支持）
   */
  getMemoryUsage(): number | undefined {
    if (typeof performance === 'undefined') return undefined;
    // @ts-ignore - performance.memory可能不存在
    if (performance.memory) {
      // @ts-ignore
      return Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
    }
    return undefined;
  }

  /**
   * 计算P95延迟
   */
  private calculateP95(values: number[]): number {
    if (values.length === 0) return 0;
    const sorted = [...values].sort((a, b) => a - b);
    const index = Math.ceil(sorted.length * 0.95) - 1;
    return sorted[Math.max(0, index)];
  }

  /**
   * 获取完整的性能指标
   */
  getMetrics(): PerformanceMetrics {
    const recentLongTasks = this.longTasks.slice(-10);
    const recentLatencies = this.inputLatencies.slice(-20);
    
    return {
      longTasks: {
        count: this.longTasks.length,
        maxDuration: Math.max(...this.longTasks, 0),
        avgDuration: this.longTasks.reduce((a, b) => a + b, 0) / this.longTasks.length || 0,
        recentTasks: recentLongTasks
      },
      fps: this.fpsCounter.getFPS(),
      inputLatency: {
        p95: this.calculateP95(this.inputLatencies),
        recent: recentLatencies
      },
      domNodeCount: this.getDOMNodeCount(),
      memoryUsage: this.getMemoryUsage()
    };
  }

  /**
   * 生成性能报告
   */
  generateReport(): string {
    const metrics = this.getMetrics();
    
    return `
性能监控报告:
═══════════════════════════════════
📊 长任务监控:
   - 总数: ${metrics.longTasks.count}
   - 最大耗时: ${metrics.longTasks.maxDuration.toFixed(2)}ms
   - 平均耗时: ${metrics.longTasks.avgDuration.toFixed(2)}ms
   - 状态: ${metrics.longTasks.maxDuration > 50 ? '❌ 需要优化' : '✅ 正常'}

🎮 帧率监控:
   - 当前FPS: ${metrics.fps.toFixed(1)}
   - 状态: ${metrics.fps >= 55 ? '✅ 流畅' : '❌ 需要优化'}

⌨️ 输入延迟:
   - P95延迟: ${metrics.inputLatency.p95.toFixed(2)}ms
   - 状态: ${metrics.inputLatency.p95 < 150 ? '✅ 响应快' : '❌ 需要优化'}

🌐 DOM节点:
   - 总数: ${metrics.domNodeCount}
   - 状态: ${metrics.domNodeCount <= 2500 ? '✅ 合理' : '❌ 过多'}

💾 内存使用:
   - ${metrics.memoryUsage ? `${metrics.memoryUsage}MB` : '无法获取'}
═══════════════════════════════════
    `.trim();
  }

  /**
   * 重置所有统计数据
   */
  reset(): void {
    this.longTasks = [];
    this.inputLatencies = [];
    this.fpsCounter.reset();
  }

  /**
   * 启用/禁用监控
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  /**
   * 销毁监控器
   */
  destroy(): void {
    this.observer?.disconnect();
    this.fpsCounter.stop();
  }
}

/**
 * FPS计数器
 */
class FPSCounter {
  private frames = 0;
  private lastTime = 0;
  private fps = 60;
  private animationId?: number;

  start(): void {
    if (typeof performance === 'undefined') return;
    this.lastTime = performance.now();
    this.tick();
  }

  private tick = (): void => {
    if (typeof performance === 'undefined' || typeof requestAnimationFrame === 'undefined') return;
    
    const now = performance.now();
    this.frames++;

    if (now - this.lastTime >= 1000) {
      this.fps = Math.round((this.frames * 1000) / (now - this.lastTime));
      this.frames = 0;
      this.lastTime = now;
    }

    this.animationId = requestAnimationFrame(this.tick);
  };

  getFPS(): number {
    return this.fps;
  }

  reset(): void {
    this.frames = 0;
    this.fps = 60;
    this.lastTime = typeof performance !== 'undefined' ? performance.now() : 0;
  }

  stop(): void {
    if (this.animationId && typeof cancelAnimationFrame !== 'undefined') {
      cancelAnimationFrame(this.animationId);
    }
  }
}

// 导出单例实例
export const performanceMonitor = PerformanceMonitor.getInstance(); 