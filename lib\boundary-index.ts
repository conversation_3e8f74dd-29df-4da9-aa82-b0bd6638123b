/**
 * BoundaryIndex - 高效的行位置索引系统
 * 用于快速计算文档中任意位置的行列坐标与字符偏移量之间的转换
 */

export interface Position {
  line: number;
  col: number;
}

export interface ChangeDesc {
  iterChanges(f: (fromA: number, toA: number, fromB: number, toB: number, inserted: any) => void): void;
}

export class BoundaryIndex {
  private lineStart: Uint32Array;
  private content: string;

  constructor(content: string) {
    this.content = content;
    this.lineStart = this.buildLineStartArray(content);
  }

  /**
   * 构建行首偏移量数组
   * lineStart[i] 表示第i行的起始字符偏移量
   */
  private buildLineStartArray(content: string): Uint32Array {
    const lines = content.split('\n');
    const lineStart = new Uint32Array(lines.length + 1);
    let offset = 0;

    for (let i = 0; i < lines.length; i++) {
      lineStart[i] = offset;
      offset += lines[i].length + 1; // +1 for '\n'
    }
    
    // 最后一个位置：如果文档末尾没有换行符，不加1
    lineStart[lines.length] = content.endsWith('\n') ? offset : offset - 1;
    
    return lineStart;
  }

  /**
   * 将行列位置转换为字符偏移量 - O(1)
   * 
   * @param pos 行列位置
   * @returns 字符偏移量
   */
  toOffset(pos: Position): number {
    if (pos.line >= this.lineStart.length - 1) {
      return this.content.length;
    }
    return this.lineStart[pos.line] + pos.col;
  }

  /**
   * 将字符偏移量转换为行列位置 - O(log N)
   * 使用二分查找
   * 
   * @param offset 字符偏移量
   * @returns 行列位置
   */
  toPos(offset: number): Position {
    if (offset <= 0) return { line: 0, col: 0 };
    if (offset >= this.content.length) {
      const lastLine = this.lineStart.length - 2;
      const lastLineStart = this.lineStart[lastLine];
      return {
        line: lastLine,
        col: this.content.length - lastLineStart
      };
    }

    // 二分查找
    let left = 0;
    let right = this.lineStart.length - 1;
    
    while (left < right) {
      const mid = Math.floor((left + right + 1) / 2);
      if (this.lineStart[mid] <= offset) {
        left = mid;
      } else {
        right = mid - 1;
      }
    }

    return {
      line: left,
      col: offset - this.lineStart[left]
    };
  }

  /**
   * 获取指定行的起始偏移量
   * 
   * @param line 行号（0-based）
   * @returns 起始偏移量
   */
  getLineStart(line: number): number {
    if (line >= this.lineStart.length - 1) {
      return this.content.length;
    }
    return this.lineStart[line];
  }

  /**
   * 获取文档总行数
   */
  getLineCount(): number {
    return this.lineStart.length - 1;
  }

  /**
   * 应用变更并增量更新索引
   * 注意：这是M2阶段的功能，M1阶段暂时重建整个索引
   * 
   * @param changes CodeMirror的变更描述
   * @param newContent 新的文档内容
   */
  applyChanges(changes: ChangeDesc, newContent: string): void {
    // M1阶段简化实现：完全重建索引
    // M2阶段将实现真正的增量更新
    this.content = newContent;
    this.lineStart = this.buildLineStartArray(newContent);
  }

  /**
   * 计算指定范围内的行数
   * 
   * @param startOffset 起始偏移量
   * @param endOffset 结束偏移量
   * @returns 行数
   */
  getLineCountInRange(startOffset: number, endOffset: number): number {
    const startPos = this.toPos(startOffset);
    const endPos = this.toPos(endOffset);
    return endPos.line - startPos.line + 1;
  }

  /**
   * 获取当前内容的副本（只读）
   */
  getContent(): string {
    return this.content;
  }

  /**
   * 调试用：打印索引信息
   */
  debug(): void {
    console.log('BoundaryIndex Debug Info:');
    console.log(`Content length: ${this.content.length}`);
    console.log(`Line count: ${this.getLineCount()}`);
    console.log('Line starts:', Array.from(this.lineStart).slice(0, 10));
  }
} 